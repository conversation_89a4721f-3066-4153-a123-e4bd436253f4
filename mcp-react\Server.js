// Remove the require statement and use proper ES module imports
import express from "express";
import React from "react";
import { renderToString } from "react-dom/server";
import App from "./src/App.js"; // Make sure to specify the correct extension

const app = express();

app.use(express.static("./build", { index: false }));

app.use((req, res) => {
  const reactApp = renderToString(<App />);
  res.send(`
    <html>
      <head><title>React App</title></head>
      <body>
        <div id="root">${reactApp}</div>
      </body>
    </html>
  `);
});

app.listen(3000, () => {
  console.log("Server is listening on port 3000");
});

// const express = require("express");
// const React = require("react");
// const { renderToString } = require("react-dom/server");
// const App = require("./src/App.js").default;

// const app = express();

// app.use(express.static("./dist", { index: false }));

// app.use((req, res) => {
//   const reactApp = renderToString(<App />);
//   res.send(`
//     <html>
//       <head><title>React App</title></head>
//       <body>
//         <div id="root">${reactApp}</div>
//       </body>
//     </html>
//   `);
// });

// app.listen(3000, () => {
//   console.log("Server is listening on port 3000");
// });