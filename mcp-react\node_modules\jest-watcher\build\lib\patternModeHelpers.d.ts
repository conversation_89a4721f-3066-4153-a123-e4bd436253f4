/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/// <reference types="node" />
export declare const printPatternCaret: (pattern: string, pipe: NodeJS.WritableStream) => void;
export declare const printRestoredPatternCaret: (pattern: string, currentUsageRows: number, pipe: NodeJS.WritableStream) => void;
